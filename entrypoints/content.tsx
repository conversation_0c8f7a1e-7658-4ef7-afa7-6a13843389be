import { defineContentScript, MatchPattern, storage } from '#imports';
import { renderAccountListPage, renderBatchDetect } from '@/components/batch-detect';
import { renderDetectContacts } from '@/components/detect-contacts/render.tsx';
import { renderEnrichContact } from '@/components/enrich-contact/render.tsx';
import { renderProspectingAccounts } from '@/components/prospecting-accounts/render';
import { initAntdPortal } from '@/utils/global-antd-portal';

import { renderSmartCreateAccount } from '@/components/smart-create-account/render.tsx';
import log from 'loglevel';
import './content-ui/style.css';

export const accountListPattern = new MatchPattern(
  '*://crm.zoho.com/crm/*/tab/Accounts/custom-view/*/list',
);

// configure log level
const originalFactory = log.methodFactory;
log.methodFactory = function (methodName, logLevel, loggerName) {
  const rawMethod = originalFactory(methodName, logLevel, loggerName);

  return function (message, ...args) {
    rawMethod(`[inhand] ${message}`, ...args);
  };
};
log.rebuild(); // Be sure to call the rebuild method in order to apply plugin.

log.setLevel(import.meta.env.MODE === 'development' ? 'debug' : 'error');

export default defineContentScript({
  matches: [
    '*://crm.zoho.com/crm/*/tab/Accounts/custom-view/*/list*',
    '*://crm.zoho.com/crm/*/tab/Accounts/*',
    '*://crm.zoho.com/crm/*/tab/Contacts/*',
    '*://crm.zoho.com/*', // 添加更广泛的匹配，确保在所有 Zoho CRM 页面上运行
    '*://one.zoho.com/*',
    '*://one.zoho.com/zohoone/*/home/<USER>/crm/*/tab/Accounts/custom-view/*/list',
    '*://one.zoho.com/zohoone/*/home/<USER>/crm/*/tab/Accounts/*',
  ],
  runAt: 'document_end',
  cssInjectionMode: 'ui', // Enable UI mode to createShadowRootUi
  allFrames: true,

  async main(ctx) {
    // if the current page is not a Zoho CRM page, return
    if (!document.querySelector('.crm-main-wrapper')) {
      return;
    }

    window.addEventListener(
      'message',
      (event) => {
        if (event.source != window) return;
        if (event.data.type === 'inhand:zohoUserID') {
          const zohoUserID = event.data.data;
          if (zohoUserID) {
            document.body.setAttribute('inhand-owner-id', zohoUserID as string);
            storage.setItem('local:zohoUserID', zohoUserID as string);
          }
        }
        if (event.data.type === 'inhand:csrfToken') {
          const csrfToken = event.data.data;
          if (csrfToken) {
            storage.setItem('local:csrfToken', csrfToken as string);
          }
        }
        if (event.data.type === 'inhand:crmZgid') {
          const crmZgid = event.data.data;
          if (crmZgid) {
            document.body.setAttribute('inhand-sales-agent', crmZgid);
            storage.setItem('local:crmZgid', crmZgid as string);
          }
        }
      },
      false,
    );

    await injectScript('/main-world.js', {
      keepInDom: true,
    });

    // Initialize the global antd portal container
    await initAntdPortal();

    renderSmartCreateAccount(ctx);
    await renderDetectContacts(ctx);
    await renderProspectingAccounts(ctx);
    await renderEnrichContact(ctx);

    await renderAccountListPage(ctx);

    renderBatchDetect(ctx, {
      accountIds: [],
    });
  },
});
