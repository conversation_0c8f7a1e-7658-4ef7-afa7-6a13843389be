import React from 'react';

import { CheckCircleOutlined, CloseCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { Button, message, Tooltip } from 'antd';

import type { AddAccountInfoType } from '../../service';
import { addAccountToCRM } from '../../service';

import type { ProspectingAccountInfo } from '../../data';

import { getUserId } from '@/utils';
import { getZohoHeaderRequest } from '@/utils/zoho-request-header';
import { capitalize } from 'es-toolkit/string';

interface AddToCrmProps {
  info: ProspectingAccountInfo;
  onSuccess?: (data?: any) => void;
}

const AddToCrm: React.FC<AddToCrmProps> = ({ info, onSuccess }) => {
  const [messageApi, contextHolder] = message.useMessage();

  const {
    loading,
    run: handleAdd,
    data,
  } = useRequest(
    async () => {
      const user_id = (await getUserId()) as string;
      const data: AddAccountInfoType = {
        user_id,
        account_info: info.account_info,
        request_headers: await getZohoHeaderRequest(),
      };
      const resp = await addAccountToCRM(data);
      if (onSuccess) {
        onSuccess(data);
      }
      if (!resp?.account_id) {
        messageApi.error(capitalize(resp?.detail) || 'Added to CRM failed');
      }
      return resp;
    },
    {
      manual: true,
      refreshDeps: [info],
    },
  );

  return (
    <>
      <Button
        type='text'
        loading={loading}
        icon={<PlusOutlined />}
        size='small'
        onClick={() => handleAdd()}
      >
        Add to CRM
      </Button>
      {data &&
        (data?.account_id ? (
          <Tooltip
            placement='left'
            title={'Added to CRM successfully'}
            getPopupContainer={(node) => node.parentElement!}
          >
            <CheckCircleOutlined
              style={{
                color: '#52c41a',
                cursor: 'pointer',
                marginLeft: 8,
              }}
            />
          </Tooltip>
        ) : (
          <Tooltip
            placement='left'
            title={capitalize(data?.detail) || 'Added to CRM failed'}
            getPopupContainer={(node) => node.parentElement!}
          >
            <CloseCircleOutlined
              style={{
                color: '#ff4d4f',
                cursor: 'pointer',
                marginLeft: 8,
              }}
            />
          </Tooltip>
        ))}
      {contextHolder}
    </>
  );
};

export default AddToCrm;
