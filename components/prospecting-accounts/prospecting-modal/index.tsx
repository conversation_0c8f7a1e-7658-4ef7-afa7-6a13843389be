import React, { useEffect, useMemo, useRef, useState } from 'react';

import type { ModalProps } from 'antd';
import { Modal, Splitter } from 'antd';

import Dialogue from './dialogue';
import List, { type ListRef } from './list';

import customCss from './index.css?raw';

interface ProspectingModalProps extends ModalProps {
  onFinish?: () => void;
}

let targetDocument: Document;
try {
  targetDocument = window.top!.document;
} catch {
  targetDocument = window.document;
}

const getStyleContainer = (): HTMLElement => targetDocument.head;

const ProspectingModal: React.FC<ProspectingModalProps> = ({ onFinish, open, ...props }) => {
  const listRef = useRef<ListRef>(null);
  const [noHistory, setNoHistory] = useState(false);
  const [activeListKey, setActiveListKey] = useState<string | undefined>();

  useEffect(() => {
    if (open) {
      const styleElement = targetDocument.createElement('style');
      styleElement.id = 'prospecting-modal-custom-styles';
      styleElement.innerHTML = customCss;

      if (!targetDocument.getElementById(styleElement.id)) {
        getStyleContainer().appendChild(styleElement);
      }

      return () => {
        const existingStyle = targetDocument.getElementById(styleElement.id);
        if (existingStyle) {
          existingStyle.remove();
        }
      };
    }
    return undefined;
  }, [open]);

  const handleListDataChange = (data: any[]) => {
    setNoHistory(data?.length === 0);
  };

  // 动态计算 Modal 宽度
  const modalWidth = noHistory ? 740 + 2 : 900 + 24 + 6;

  // 动态计算 Dialogue 宽度
  const dialogueWidth = noHistory ? 740 + 2 : 692 + 2;

  const modalContent = useMemo(
    () => (
      <Splitter
        style={{
          height: 600 + 2,
        }}
        className={noHistory ? 'hidden-splitter' : 'splitter-container'}
      >
        <Splitter.Panel
          defaultSize={noHistory ? 0 : 162}
          min={noHistory ? 0 : 162}
          max={noHistory ? 0 : 400}
          style={{
            display: noHistory ? 'none' : 'block',
          }}
        >
          <List
            activeKey={activeListKey}
            onSelectChange={(v) => {
              setActiveListKey(v);
            }}
            onDataChange={handleListDataChange}
            ref={listRef}
          />
        </Splitter.Panel>
        <Splitter.Panel
          defaultSize={dialogueWidth}
          style={{
            overflowX: 'hidden',
          }}
          className='dialogue-panel'
        >
          <Dialogue
            threadId={activeListKey}
            onChangeThreadId={setActiveListKey}
            refreshList={(threadId: any) => {
              listRef?.current?.fetchHistory(threadId);
            }}
          />
        </Splitter.Panel>
      </Splitter>
    ),
    [noHistory, activeListKey, dialogueWidth],
  );

  return (
    <Modal
      {...props}
      open={open}
      onCancel={onFinish}
      title='Lookalike Customer'
      width={modalWidth}
      footer={false}
      maskClosable={false}
      styles={{
        body: {
          height: 600 + 2,
        },
        footer: {
          marginTop: 0,
        },
      }}
    >
      {modalContent}
    </Modal>
  );
};

export default ProspectingModal;
