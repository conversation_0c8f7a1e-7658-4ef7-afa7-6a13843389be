import type { TaskStatusType } from '../detect-contacts/data';

export { TaskStatusType };

export interface ProspectingAccountInfo {
  content: string;
  account_info: ProspectingAccountProfile;
}

export interface ProspectingAccountProfile {
  name: string;
  website: string;
  linkedin: string;
  account_type: string;
  phone: string;
  industry: string;
  market_segments: string[];
  founded_year: number;
  organization_revenue: string;
  location: string;
  territory: string;
  address_state: string;
  description: string;
}

export interface ProspectingAccountType {
  title: string;
  user_query: string;
  completed_at: string;
  similar_companies: ProspectingAccountInfo[];
}

export interface HistoryItem {
  _id: string;
  title?: string;
}

export interface EventStreamItem {
  event: string;
  status: TaskStatusType;
  message?: string;
  content?: string;

  [Key: string]: unknown;
}
