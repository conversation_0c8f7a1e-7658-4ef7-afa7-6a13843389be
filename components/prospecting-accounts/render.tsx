import type { ContentScriptContext } from '#imports';
import { createAutoMountShadowRootUI } from '@/utils';
import ReactDOM from 'react-dom/client';
import ProspectingAccounts from './index';

// maintain a global state to track if the UI is mounted
export const renderProspectingAccounts = async (ctx: ContentScriptContext) => {
  await createAutoMountShadowRootUI(ctx, {
    name: 'inhand-prospecting-accounts-ui',
    position: 'inline',
    pathMatches: ['/crm/*/tab/Accounts/*'],
    anchor: 'crm-detailview-actions[module="Accounts"]',
    append: 'first',
    inheritStyles: true,
    isolateEvents: false,
    onMount: (container: HTMLElement, shadow: ShadowRoot, shadowHost: HTMLElement) => {
      if (shadowHost) {
        shadowHost.style.display = 'inline-block';
        shadowHost.style.zIndex = '99'; // 确保按钮在最上层
        shadowHost.style.marginRight = '10px'; // 调整位置
        shadowHost.classList.add('inhand-prospecting-accounts-host');
      }

      if (container) {
        container.style.display = 'inline-block';
        container.style.position = 'relative';
        container.style.verticalAlign = 'middle';
        // 新增：独立 createRoot 渲染
        const reactContainer = document.createElement('div');
        reactContainer.id = 'inhand-prospecting-accounts-ui-react-container';
        container.appendChild(reactContainer);
        const root = ReactDOM.createRoot(reactContainer);
        root.render(<ProspectingAccounts />);
      }
    },
  });
};
