import type { StreamMessage } from '@/components/detect-contact-popover/data';
// import TaskStatusTimeline from '@/components/detect-contact-popover/task-status-timeline';
import MarkdownView from '@/components/markdown-view';
import type { ProspectingAccountProfile } from '@/components/prospecting-accounts/data';
import AddToCrm from '@/components/prospecting-accounts/prospecting-modal/add-to-crm';
import { formatterProspectingData } from '@/components/prospecting-accounts/prospecting-modal/dialogue';
import { getUserId } from '@/utils';
import { BASE_URL, X_API_KEY } from '@/utils/request';
import { getZohoHeaderRequest } from '@/utils/zoho-request-header.tsx';
import { Sender } from '@ant-design/x';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { useMount, useRequest } from 'ahooks';
import { <PERSON><PERSON>, But<PERSON>, Card, Modal, Space, Spin, Typography } from 'antd';
import { isArray } from 'es-toolkit/compat';
import { capitalize } from 'es-toolkit/string';
import React, { useMemo, useRef, useState } from 'react';
import MagicIcon from '~/assets/magic-colorful.svg?react';
import customCss from './index.css?raw';

let targetDocument: Document;
try {
  targetDocument = window.top!.document;
} catch {
  targetDocument = window.document;
}
const getStyleContainer = (): HTMLElement => targetDocument.head;

type ErrorType = 'not_found' | 'multiple_results' | 'exists' | 'unknown_error' | 'network';

const errorMsgMap: Record<ErrorType, any> = {
  not_found: 'Company not found',
  multiple_results: 'Multiple accounts found',
  exists: 'Account already exists',
  unknown_error: 'Unknown error',
  network: 'Connect error',
};

const SmartCreateAccount: React.FC = () => {
  const [open, setOpen] = useState<boolean>(false);
  const [prompt, setPrompt] = useState<string | undefined>();
  // const [messages, setMessages] = useState<StreamMessage[]>([]);
  const [loadingMessageStream, setLoadingMessageStream] = useState<StreamMessage>();
  const [error, setError] = useState<
    | {
        type: ErrorType;
        message: string;
        data?: {
          name?: string;
          account_id?: string;
        };
      }
    | undefined
  >();
  const [multipleAccount, setMultipleAccount] = useState<ProspectingAccountProfile[]>([]);
  // const [collapseKeys, setCollapseKeys] = useState<string[]>(['thinking-timeline']);

  useMount(() => {
    const styleElement = targetDocument.createElement('style');
    styleElement.id = 'prospecting-modal-dialogue-custom-styles';
    styleElement.innerHTML = customCss;

    if (!targetDocument.getElementById(styleElement.id)) {
      getStyleContainer().appendChild(styleElement);
    }

    return () => {
      const existingStyle = targetDocument.getElementById(styleElement.id);
      if (existingStyle) {
        existingStyle.remove();
      }
    };
  });

  const ctrlRef = useRef<AbortController | null>(null);

  const {
    loading,
    run: handleSubmit,
    cancel: cancelSubmit,
  } = useRequest(
    async (userInput) => {
      if (ctrlRef.current) {
        ctrlRef.current.abort();
      }
      // setMessages([]);
      setLoadingMessageStream(undefined);

      const ctrl = new AbortController();
      ctrlRef.current = ctrl;

      const userId = (await getUserId()) as string;
      const requestHeaders = await getZohoHeaderRequest();
      const submitData = {
        user_input: userInput,
        user_id: userId,
        request_headers: requestHeaders,
      };

      const streamURL = `${BASE_URL}/api/sales-agent/add-account-by-user-input`;
      await fetchEventSource(streamURL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': X_API_KEY,
        },
        body: JSON.stringify(submitData),
        signal: ctrl.signal,
        onopen: async () => {
          setLoadingMessageStream({
            type: 'thinking',
            content: 'Start analysis',
            timestamp: new Date().toDateString(),
          });
          // setMessages([
          //   {
          //     type: 'thinking',
          //     content: 'Start analysis',
          //     timestamp: new Date().toDateString(),
          //   },
          // ]);
        },
        onmessage: async (event) => {
          const data: StreamMessage = JSON.parse(event.data);
          setLoadingMessageStream(data);
          // setMessages((prev) => {
          //   return [...prev, data];
          // });

          if (data.type === 'error') {
            const errorType = (data.content ?? 'unknown_error') as ErrorType;
            const errorMsg = errorMsgMap[errorType];
            setError({
              type: errorType,
              message: errorMsg,
              data: data?.data,
            });
          }
          if (data.type === 'finish') {
            const multipleAccount = data?.data?.result;
            const account_id = data?.data?.account_id;
            if (multipleAccount && isArray(multipleAccount)) {
              // setCollapseKeys([]);
              setMultipleAccount(multipleAccount);
            } else {
              if (account_id) {
                window.location.href = `https://crm.zoho.com/crm/org663291548/tab/Accounts/${
                  account_id
                }`;
              }
              setOpen(false);
              setPrompt(undefined);
              setLoadingMessageStream(undefined);
              // setMessages([]);
            }
          }
        },
        onerror: (event) => {
          console.error('[inhand] SSE connection error', event);
          setError({
            type: 'network',
            message: 'SSE connection error',
          });
        },
        onclose: async () => {
          console.info('[inhand] SSE connection closed');
        },
      });
    },
    {
      manual: true,
      refreshDeps: [],
    },
  );

  const renderMultipleAccountCard = useMemo(() => {
    if (multipleAccount?.length > 1) {
      return (
        <>
          <Typography.Title
            level={5}
            style={{
              marginTop: 0,
            }}
          >
            We have found {multipleAccount?.length} companies:
          </Typography.Title>
          {multipleAccount.map((item: ProspectingAccountProfile, index) => {
            const content = formatterProspectingData(item);
            return (
              <Card
                // eslint-disable-next-line react/no-array-index-key
                key={`account-card-${index}`}
                size='small'
                title={
                  <div
                    style={{
                      fontWeight: 600,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 8,
                    }}
                  >
                    <span
                      style={{
                        fontSize: 18,
                        color: '#999',
                        flexShrink: 0,
                        lineHeight: '1.4',
                      }}
                    >
                      {index + 1}.
                    </span>
                    <Typography.Text
                      ellipsis
                      style={{ width: 500, fontSize: 18, lineHeight: '1.4' }}
                    >
                      {item.name}
                    </Typography.Text>
                  </div>
                }
                extra={
                  <AddToCrm
                    info={{ account_info: item, content: content }}
                    key={item.website}
                    // onSuccess={({ account_id }) => {
                    //   if (account_id) {
                    //     window.location.href = `https://crm.zoho.com/crm/org663291548/tab/Accounts/${account_id}`;
                    //   }
                    // }}
                  />
                }
                style={{
                  width: '100%',
                }}
              >
                <MarkdownView content={content} className='dialogue-markdown-view' />
              </Card>
            );
          })}
        </>
      );
    }
  }, [multipleAccount]);

  // const renderTimeLine = useMemo(
  //   () =>
  //     messages?.length > 0 && (
  //       <Collapse
  //         ghost
  //         defaultActiveKey={['thinking-timeline']}
  //         activeKey={collapseKeys}
  //         onChange={(keys) => {
  //           setCollapseKeys(keys as string[]);
  //         }}
  //         style={{
  //           marginBottom: 16,
  //         }}
  //         items={[
  //           {
  //             key: 'thinking-timeline',
  //             label: 'Thought Trace',
  //             children: (
  //               <TaskStatusTimeline
  //                 messages={messages}
  //                 loading={loading}
  //                 error={error}
  //                 style={{
  //                   width: '100%',
  //                   maxHeight: 800,
  //                   overflowY: 'auto',
  //                 }}
  //               />
  //             ),
  //           },
  //         ]}
  //       />
  //     ),
  //   [messages, loading, error, collapseKeys],
  // );

  return (
    <>
      <Button
        icon={<MagicIcon width={20} height={20} />}
        onClick={() => {
          setOpen(true);
        }}
      >
        Smart Create
      </Button>
      {open && (
        <Modal
          title='Smart Create Account'
          open={open}
          onCancel={() => {
            setOpen(false);
            setMultipleAccount([]);
            setError(undefined);
            // setMessages([]);
            setLoadingMessageStream(undefined);
            setPrompt(undefined);
          }}
          footer={false}
          width={600}
          styles={{
            body: {
              maxHeight: 800,
              overflowY: 'auto',
            },
          }}
        >
          {loading && (
            <Spin spinning={loading} tip={loadingMessageStream?.content ?? 'Loading'} size='large'>
              <div style={{ height: 100 }} />
            </Spin>
          )}
          <Space direction='vertical' size='middle' style={{ display: 'flex' }}>
            {/*{renderTimeLine}*/}
            {renderMultipleAccountCard}
            {error && (
              <Alert
                message={
                  error?.type === 'exists' ? (
                    <span>
                      This account ({error?.data?.name ?? ''}) already exists; you can click the{' '}
                      <Typography.Link
                        href={`https://crm.zoho.com/crm/org663291548/tab/Accounts/${error?.data?.account_id}`}
                        target='_blank'
                      >
                        View in CRM
                      </Typography.Link>{' '}
                      to view it.
                    </span>
                  ) : (
                    capitalize(error?.message)
                  )
                }
                type='error'
              />
            )}
            {multipleAccount?.length > 0 ? undefined : (
              <Sender
                loading={loading}
                value={prompt}
                onChange={(v) => {
                  // setMessages([]);
                  setMultipleAccount([]);
                  setLoadingMessageStream(undefined);
                  setError(undefined);
                  setPrompt(v);
                }}
                onSubmit={() => {
                  if (prompt) {
                    // setMessages([]);
                    setMultipleAccount([]);
                    setLoadingMessageStream(undefined);
                    setError(undefined);
                    handleSubmit(prompt);
                  }
                }}
                onCancel={() => {
                  ctrlRef.current?.abort();
                  cancelSubmit();
                }}
                readOnly={loading}
                placeholder='Please enter as much information about the company as you can: name, address, website, industry, market segment, etc.'
                autoSize={{
                  minRows: 2,
                  maxRows: 2,
                }}
              />
            )}
          </Space>
        </Modal>
      )}
    </>
  );
};

export default SmartCreateAccount;
