import type { ContentScriptContext } from '#imports';
import { createAutoMountShadowRootUI } from '@/utils';
import { waitElement } from '@1natsu/wait-element';
import ReactDOM from 'react-dom/client';
import AddAccount from './index';

export const renderSmartCreateAccount = async (ctx: ContentScriptContext) => {
  // id=moreActionsDiv
  await waitElement('#table_row_1 .customPluswithImpotBtnCon');

  await createAutoMountShadowRootUI<ReactDOM.Root | undefined>(ctx, {
    name: 'inhand-smart-create-account-ui',
    position: 'inline',
    anchor: '#table_row_1 .customPluswithImpotBtnCon',
    pathMatches: ['/crm/*/tab/Accounts/custom-view/*/list'],
    append: 'after',
    inheritStyles: true,
    isolateEvents: true,
    onMount: (container: HTMLElement, shadow: ShadowRoot, shadowHost: HTMLElement) => {
      // 设置host元素样式
      if (shadowHost) {
        shadowHost.classList.add('inhand-injected-element');
        shadowHost.setAttribute('data-inhand-injected', 'true');
        shadowHost.style.zIndex = '1000';
        shadowHost.style.display = 'inline-block';
        shadowHost.style.verticalAlign = 'middle';
        shadowHost.style.marginRight = '10px';
      }

      if (container) {
        let shadowHead = shadow.querySelector('head');
        if (!shadowHead) {
          shadowHead = document.createElement('head');
          shadow.appendChild(shadowHead);
        }
        // 添加事件隔离
        const eventShield = document.createElement('div');
        eventShield.className = 'event-shield';
        container.appendChild(eventShield);

        // 创建一个容器元素，所有渲染都在这个容器内
        const reactContainer = document.createElement('div');
        reactContainer.id = 'inhand-smart-create-account-container';

        const root = ReactDOM.createRoot(reactContainer);

        root.render(<AddAccount />);

        container.appendChild(reactContainer);
        return root;
      }

      return container;
    },
    onRemove: (root: ReactDOM.Root | undefined) => {
      root?.unmount();
    },
  });
};
