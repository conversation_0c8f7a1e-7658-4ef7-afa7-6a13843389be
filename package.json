{"name": "inhand-sales-assistant", "version": "0.5.0", "private": true, "description": "InHand Sales Assistant is a browser extension.", "type": "module", "scripts": {"build": "cross-env NODE_ENV=production wxt build && wxt zip && pnpm run build:edge && pnpm run build:firefox && pnpm run build:safari", "build:edge": "wxt zip -b edge", "build:firefox": "cross-env NODE_ENV=production wxt build -b firefox && wxt zip -b firefox", "build:safari": "cross-env NODE_ENV=production wxt build -b safari && wxt zip -b safari", "clean": "wxt clean", "compile": "tsc --noEmit", "dev": "cross-env NODE_ENV=development ENV=local wxt --port 8000 --mode development", "dev:firefox": "cross-env NODE_ENV=development ENV=local wxt -b firefox --port 8000", "dev:local": "cross-env NODE_ENV=development ENV=local wxt --port 8000 --mode development", "dev:safari": "cross-env NODE_ENV=development ENV=local wxt -b safari --port 8000", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "postinstall": "wxt prepare", "lint": "pnpm run lint:check", "lint:check": "eslint --ext .ts,.tsx . && pnpm run stylelint:check && prettier --check \"**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "lint:fix": "eslint --ext .ts,.tsx . --fix && pnpm run stylelint:fix && pnpm run format", "prepare": "wxt prepare && husky", "start": "pnpm run dev", "stylelint:check": "stylelint \"components/**/*.css\" \"entrypoints/**/*.css\"", "stylelint:fix": "stylelint \"components/**/*.css\" \"entrypoints/**/*.css\" --fix", "zip": "wxt zip", "zip:firefox": "wxt zip -b firefox", "zip:safari": "wxt zip -b safari"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,css,scss,md}": ["prettier --write"]}, "dependencies": {"@1natsu/wait-element": "^4.1.2", "@ant-design/cssinjs": "^1.24.0", "@ant-design/icons": "~5.6.1", "@ant-design/x": "^1.6.0", "@microsoft/fetch-event-source": "^2.0.1", "@types/markdown-it": "^14.1.2", "@wxt-dev/storage": "^1.1.1", "@wxt-dev/webextension-polyfill": "^1.0.0", "ahooks": "^3.9.0", "antd": "^5.26.6", "antd-style": "^3.7.1", "axios": "^1.10.0", "dayjs": "^1.11.13", "es-toolkit": "^1.39.7", "loglevel": "^1.9.2", "markdown-it": "^14.1.0", "markdown-it-external-link": "^1.1.0", "minimatch": "^10.0.3", "mitt": "^3.0.1", "psl": "^1.15.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown-typewriter": "^1.1.4", "react-simple-typewriter": "^5.0.1", "react-use": "^17.6.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "webextension-polyfill": "^0.12.0"}, "devDependencies": {"@babel/eslint-parser": "^7.28.0", "@babel/eslint-plugin": "^7.27.1", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.31.0", "@types/chrome": "^0.1.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@vitejs/plugin-react": "^4.7.0", "@wxt-dev/auto-icons": "^1.0.2", "@wxt-dev/module-react": "^1.1.3", "cross-env": "^7.0.3", "eslint": "^9.31.0", "eslint-config-next": "^15.4.2", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jest": "^29.0.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-you-might-not-need-an-effect": "^0.4.1", "eslint-plugin-unicorn": "^59.0.1", "globals": "^16.3.0", "husky": "^9.1.7", "postcss-scss": "^4.0.9", "prettier": "^3.6.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-packagejson": "^2.5.19", "stylelint": "^16.22.0", "stylelint-config-css-modules": "^4.5.1", "stylelint-config-prettier": "^9.0.5", "stylelint-config-standard": "^38.0.0", "stylelint-config-standard-scss": "^15.0.1", "stylelint-declaration-block-no-ignored-properties": "^2.8.0", "stylelint-prettier": "^5.0.3", "typescript": "^5.8.3", "typescript-eslint": "^8.37.0", "vite-plugin-svgr": "^4.3.0", "wxt": "^0.20.7", "zod": "^4.0.5"}, "packageManager": "pnpm@10.14.0", "engines": {"node": ">=18.0.0", "pnpm": ">=10.11.0"}}