import { storage } from '#imports';
import { getRequestCookies } from './storage';

// 实时获取 csrfToken
export const getRealTimeCsrfToken = (): Promise<string> => {
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      window.removeEventListener('message', messageHandler);
      reject(new Error('Timeout'));
    }, 2000);

    const messageHandler = (event: MessageEvent) => {
      if (event.source !== window || event.data.type !== 'inhand:csrfTokenResponse') return;

      clearTimeout(timeout);
      window.removeEventListener('message', messageHandler);
      resolve(event.data.data || '');
    };

    window.addEventListener('message', messageHandler);
    window.postMessage({ type: 'inhand:getCsrfToken' }, '*');
  });
};

export const getZohoHeaderRequest = async () => {
  const requiredCookies = await getRequestCookies();

  let csrfToken: string;
  try {
    csrfToken = await getRealTimeCsrfToken();
  } catch {
    csrfToken = (await storage.getItem('local:csrfToken')) || '';
  }

  const crmZgid = await storage.getItem('local:crmZgid');

  return {
    Cookie: requiredCookies?.map((cookie: any) => `${cookie.name}=${cookie.value}`).join('; '),
    'X-Zcsrf-Token': `crmcsrfparam=${csrfToken}`,
    'User-Agent': navigator.userAgent,
    'X-CRM-ORG': crmZgid ?? 663291548,
  };
};
